{
  // ==================== 项目基础信息 ====================
  // 来源：Step1_基础信息与工具识别专家分析结果
  "projectInfo": {
    "projectName": "@mcpcn/mcp-bilibili",                  // 项目名称，从package.json的name字段提取
    "projectUUId": "@mcpcn/mcp-bilibili",                  // 项目唯一标识符，通常与项目名称相同，用于数据库关联
    "version": "1.0.5",                                    // 项目版本号，从package.json的version字段提取
    "description": "B站开放平台 MCP 服务器，支持用户认证、视频管理、完整的视频投稿流程", // 项目英文描述，从package.json的description字段提取
    "descriptionChinese": "B站开放平台的MCP（模型上下文协议）集成服务器，支持用户的OAuth认证、视频资源管理和全流程视频投稿等功能", // 项目中文描述，提供准确的中文翻译
    "Project_introduction": "# B站智能投稿助手\n\n## 项目简介\n这是一个智能B站投稿与视频管理工具，帮助你用最简单的方式完成B站的登录授权、用户信息查询和完整的视频投稿流程，无需手动操作网页即可实现快捷投稿。\n\n## 主要功能\n通过对话式交互，你可以：\n- 授权并安全登录你的B站账号，只需扫码即可完成\n- 获取你的B站用户信息和账号数据\n- 查询你已发布过的视频列表\n- 获取所有可用的视频投稿分区，方便正确选择栏目\n- 实现大视频文件的分片上传和合并\n- 上传视频封面图片并一键完成投稿\n\n## 如何使用\n1. 首次登录时，先用扫码方式完成B站授权。\n2. 系统会自动检测和缓存Token，避免频繁授权。\n3. 登录成功后可以查询用户信息、历史视频，也可以按引导上传新视频、添加标题标签、选择分区等，最后一步提交稿件即可成功投稿。\n\n## 使用场景\n当你需要高效地在B站上传视频、发布稿件，或需要快速查看账号和视频数据时，这个工具可为你省去大量手动步骤，让投稿管理变得极为轻松快捷。", // 面向最终用户的项目介绍，Markdown格式，基于代码库实际内容生成
    "totalTools": 12,                                      // 项目中工具的总数量，必须与实际识别到的工具数量一致
    "primaryDomain": "B站视频投稿与管理",                   // 项目主要应用领域，描述项目的核心功能范围
    "complexityLevel": "复杂"                               // 项目复杂度等级（简单/中等/复杂），基于工具数量、功能复杂度等评估
  },
  // ==================== 项目能力特征 ====================
  // 来源：Step2_功能特性与处理能力分析专家分析结果
  "projectCapabilities": {
    "hasFileProcessing": 1,                                 // 是否具有文件处理能力（0=否，1=是）- 支持视频和图片文件上传
    "hasAPIIntegration": 1,                                 // 是否集成外部API（0=否，1=是）- 集成B站开放平台API
    "hasDataProcessing": 1,                                 // 是否具有数据处理能力（0=否，1=是）- 处理用户数据、视频信息等
    "hasWorkflowSupport": 1,                                // 是否支持工作流（0=否，1=是）- 支持完整的视频投稿工作流
    "supportedPlatforms": "mac,windows,linux",             // 支持的操作系统平台，逗号分隔
    "hasSystemDependencies": 1,                            // 是否有系统依赖（0=否，1=是）- 需要文件系统访问权限
    "requiresExternalAPIs": 1,                              // 是否需要外部API（0=否，1=是）- 依赖B站开放平台API
    "deploymentComplexity": "中等",                         // 部署复杂度（简单/中等/复杂）- 需要API密钥配置
    "overallSecurityLevel": "安全"                          // 整体安全级别（安全/中等/危险）- 使用官方API，安全可靠
  },
  // ==================== 工具详细信息 ====================
  // 来源：Step1、Step2、Step3综合分析结果
  "tools": [
    {
      // --- 工具基础标识信息 ---
      "ID": null,                                           // 数据库主键ID，初始为null，由数据库分配
      "toolId": "tool_001",                                 // 工具唯一标识符，格式为tool_xxx
      "c_name": "检查登录状态",                              // 工具中文名称，用户友好的日常用语表达
      "name": "bilibili_check_local_token",                 // 工具英文名称，不包含项目前缀
      "fullName": "@mcpcn/mcp-bilibili--bilibili_check_local_token", // 完整工具名称，格式为"项目名--工具名"
      "description": "Check if there is a valid local Bilibili access token", // 工具英文描述，简洁实用的功能说明
      "descriptionChinese": "自动检测本地是否已登录B站账号，如果有则无需重复登录", // 工具中文描述，用户视角的功能说明
      "category": "认证类",                                  // 工具分类（查询类/操作类/辅助类/配置类/认证类等）

      // --- 工具参数定义 ---
      "inputSchema": {                                      // 工具输入参数的JSON Schema定义
        "type": "object",
        "properties": {},                                   // 无需参数的工具，properties为空对象
        "required": []                                      // 必需参数列表，此工具无必需参数
      },

      // --- 工具功能特征 ---
      "keywords": "检查登录,检测token,本地账号,免扫码,是否已登录", // 功能关键词，用逗号分隔，用于搜索和分类
      "canHandleDirectory": 0,                              // 是否能处理目录（0=否，1=是）
      "multiFileType": 0,                                   // 是否支持多文件类型（0=否，1=是）
      "supportedExtensions": null,                          // 支持的文件扩展名，null表示不处理文件
      "apiIntegration": 1,                                  // 是否集成API（0=否，1=是）- 检查本地token状态
      "dataProcessing": 1,                                  // 是否进行数据处理（0=否，1=是）- 处理token验证

      // --- 工具执行特性 ---
      "canDirectExecute": 1,                                // 是否可直接执行（0=否，1=是），无需通过大模型
      "isDangerous": 0,                                     // 是否为危险操作（0=否，1=是）
      "platforms": "mac,windows,linux",                    // 支持的平台，逗号分隔
      "isDisabled": 0,                                      // 是否被禁用（0=否，1=是）
      "securityLevel": "安全",                              // 安全级别（安全/中等/危险）
      "executionType": "数据处理",                          // 执行类型（API调用/系统操作/数据处理/文件操作）
      "is_it_available": true,                              // 工具是否可用，基于错误处理机制评估
      "prerequisiteToolId": null,                           // 前置依赖工具ID，null表示无依赖
      "dependencies": [],                                   // 依赖关系数组
      "regex": null,                                        // 正则表达式模式，null表示不使用
      "is_single_call": 1,                                  // 是否为单次调用（0=否，1=是），MCP工具默认为1
      "projectId": null                                     // 关联的项目ID，由数据库分配
    },
    {
      // 工具002：扫码登录 - 认证流程的第二步
      "ID": null,                                           // 数据库主键ID
      "toolId": "tool_002",                                 // 工具唯一标识符
      "c_name": "扫码登录",                                  // 中文名称：用户友好表达
      "name": "bilibili_web_authorize_link",               // 英文名称：API函数名
      "fullName": "@mcpcn/mcp-bilibili--bilibili_web_authorize_link", // 完整名称
      "description": "Generate Bilibili web authorization link and open browser for QR code login", // 英文描述
      "descriptionChinese": "生成B站授权登录二维码，帮你快捷进入扫码登录页面", // 中文描述
      "category": "认证类",                                  // 工具分类
      "inputSchema": {                                      // 参数定义：无需输入参数
        "type": "object",
        "properties": {},
        "required": []
      },
      "keywords": "扫码登录,手机登录,账号授权,网页登录,扫码用手机授权,安全登录", // 功能关键词
      "canHandleDirectory": 0,                              // 不处理目录
      "multiFileType": 0,                                   // 不处理多文件
      "supportedExtensions": null,                          // 不处理文件
      "apiIntegration": 1,                                  // 集成API：调用B站授权API
      "dataProcessing": 0,                                  // 不进行数据处理
      "canDirectExecute": 1,                                // 可直接执行
      "isDangerous": 0,                                     // 非危险操作
      "platforms": "mac,windows,linux",                    // 跨平台支持
      "isDisabled": 0,                                      // 未禁用
      "securityLevel": "安全",                              // 安全级别
      "executionType": "API调用",                           // 执行类型：API调用
      "is_it_available": true,                              // 工具可用
      "prerequisiteToolId": null,                           // 无前置依赖
      "dependencies": [],                                   // 无依赖关系
      "regex": null,                                        // 不使用正则
      "is_single_call": 1,                                  // 单次调用
      "projectId": null                                     // 项目ID待分配
    },
    {
      "ID": null,
      "toolId": "tool_003",
      "c_name": "获取访问令牌",
      "name": "bilibili_web_poll_and_token",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_web_poll_and_token",
      "description": "Poll for code and exchange for access token after web authorization; requires state parameter",
      "descriptionChinese": "扫码授权后，自动获取B站登录凭证Token，完成登录",
      "category": "认证类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "state": { "type": "string", "description": "授权时生成的state" }
        },
        "required": ["state"]
      },
      "keywords": "获取令牌,拿登录token,登录凭证,授权后token,获取access_token",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_002",
      "dependencies": ["tool_002"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_004",
      "c_name": "查个人信息",
      "name": "bilibili_get_user_info",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_get_user_info",
      "description": "Get Bilibili user base info (nickname, avatar, openid)",
      "descriptionChinese": "获取你在B站的昵称、头像等基本个人信息",
      "category": "查询类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" }
        },
        "required": ["access_token"]
      },
      "keywords": "查个人信息,查昵称,头像,我的账号信息,基本资料,openID查询",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_005",
      "c_name": "查账号数据",
      "name": "bilibili_get_user_stat",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_get_user_stat",
      "description": "Get user statistics like followers, following, and videos count",
      "descriptionChinese": "查看你的粉丝数、关注数和投稿总数等账号核心数据",
      "category": "查询类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" }
        },
        "required": ["access_token"]
      },
      "keywords": "查粉丝数,关注数,作品数,账号数据,我有多少粉,统计查询",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_006",
      "c_name": "查投稿历史",
      "name": "bilibili_get_video_list",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_get_video_list",
      "description": "Get the list of submitted videos with detailed info",
      "descriptionChinese": "查看你在B站发布过的所有视频稿件和历史信息",
      "category": "查询类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" }
        },
        "required": ["access_token"]
      },
      "keywords": "查投稿历史,看我的视频,历史作品,已发布视频,投稿列表",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_007",
      "c_name": "查投稿分区",
      "name": "bilibili_get_video_categories",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_get_video_categories",
      "description": "Get the list of Bilibili video sections/categories for submissions",
      "descriptionChinese": "获取所有可用的B站投稿分区，便于视频准确分类",
      "category": "查询类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" }
        },
        "required": ["access_token"]
      },
      "keywords": "查投稿分区,视频分区,选择分区,分区类型,选栏目,分类选择",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_008",
      "c_name": "上传前准备",
      "name": "bilibili_upload_video_preprocess",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_upload_video_preprocess",
      "description": "Pre-process before video upload to get upload token",
      "descriptionChinese": "上传视频前的准备步骤，获取上传令牌",
      "category": "操作类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" },
          "filename": { "type": "string", "description": "视频文件名" }
        },
        "required": ["access_token", "filename"]
      },
      "keywords": "上传准备,视频预处理,获取上传令牌,准备上传,视频设置,分块上传准备",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": "mp4,mov,avi,flv",
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_009",
      "c_name": "上传视频分片",
      "name": "bilibili_upload_video_chunk",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_upload_video_chunk",
      "description": "Upload a video chunk file after obtaining the upload token",
      "descriptionChinese": "分段上传大文件视频，每次提交一个分片",
      "category": "操作类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "upload_token": { "type": "string", "description": "视频上传令牌（从预处理接口获取）" },
          "video_file_path": { "type": "string", "description": "本地视频文件路径，如: /path/to/video.mp4" },
          "part_number": { "type": "integer", "description": "分片编号，默认为1", "default": 1 }
        },
        "required": ["upload_token", "video_file_path"]
      },
      "keywords": "大文件上传,分片上传,上传视频,上传文件,分段上传,上传高清视频",
      "canHandleDirectory": 0,
      "multiFileType": 1,
      "supportedExtensions": "mp4,mov,avi,flv",
      "apiIntegration": 1,
      "dataProcessing": 0,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "文件操作",
      "is_it_available": true,
      "prerequisiteToolId": "tool_008",
      "dependencies": ["tool_008"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_010",
      "c_name": "合并视频分片",
      "name": "bilibili_complete_video_upload",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_complete_video_upload",
      "description": "Complete merging video file chunks after all parts are uploaded",
      "descriptionChinese": "将所有视频分片合成一个完整视频",
      "category": "操作类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "upload_token": { "type": "string", "description": "视频上传令牌（从预处理接口获取）" }
        },
        "required": ["upload_token"]
      },
      "keywords": "合并分片,视频合成,合成视频,完整视频,视频分片合并,大文件合成",
      "canHandleDirectory": 0,
      "multiFileType": 1,
      "supportedExtensions": "mp4,mov,avi,flv",
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_009",
      "dependencies": ["tool_009"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_011",
      "c_name": "上传封面图片",
      "name": "bilibili_upload_cover",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_upload_cover",
      "description": "Upload a cover image file for your Bilibili video",
      "descriptionChinese": "上传视频的封面图片，支持JPEG、PNG格式",
      "category": "操作类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" },
          "cover_file_path": { "type": "string", "description": "本地图片文件路径，如: /path/to/cover.jpg" }
        },
        "required": ["access_token", "cover_file_path"]
      },
      "keywords": "上传封面,封面图片,视频封面,上传图片,设置封面,更换封面",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": "jpg,jpeg,png",
      "apiIntegration": 1,
      "dataProcessing": 0,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "文件操作",
      "is_it_available": true,
      "prerequisiteToolId": "tool_003",
      "dependencies": ["tool_003"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    },
    {
      "ID": null,
      "toolId": "tool_012",
      "c_name": "一键提交稿件",
      "name": "bilibili_submit_archive",
      "fullName": "@mcpcn/mcp-bilibili--bilibili_submit_archive",
      "description": "Submit a video to Bilibili after upload and merge. Complete the video publishing process.",
      "descriptionChinese": "填写标题、分区等信息后，一步完成视频在B站的正式投稿",
      "category": "操作类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "access_token": { "type": "string", "description": "Access-Token" },
          "upload_token": { "type": "string", "description": "视频上传令牌（从预处理接口获取，完成上传和合片后使用）" },
          "title": { "type": "string", "description": "视频标题，长度小于80" },
          "desc": { "type": "string", "description": "视频描述，长度小于250（可选）" },
          "cover": { "type": "string", "description": "封面图片URL（可选，建议提供）" },
          "tag": { "type": "string", "description": "视频标签，多个标签用英文逗号分隔，总长度小于200" },
          "tid": { "type": "integer", "description": "分区ID，可通过bilibili_get_video_categories获取" },
          "copyright": { "type": "integer", "description": "版权类型：1-原创，2-转载", "default": 1 },
          "no_reprint": { "type": "integer", "description": "是否禁止转载：0-允许转载，1-禁止转载", "default": 0 },
          "source": { "type": "string", "description": "转载来源（copyright为2时必填）" }
        },
        "required": ["access_token", "upload_token", "title", "tag", "tid"]
      },
      "keywords": "一键投稿,提交视频,发布作品,视频投稿,快速投稿,完成投稿",
      "canHandleDirectory": 0,
      "multiFileType": 0,
      "supportedExtensions": null,
      "apiIntegration": 1,
      "dataProcessing": 1,
      "canDirectExecute": 1,
      "isDangerous": 0,
      "platforms": "mac,windows,linux",
      "isDisabled": 0,
      "securityLevel": "安全",
      "executionType": "API调用",
      "is_it_available": true,
      "prerequisiteToolId": "tool_010",
      "dependencies": ["tool_008", "tool_009", "tool_010", "tool_011"],
      "regex": null,
      "is_single_call": 1,
      "projectId": null
    }
  ],
  "toolRelationships": {
    "hasWorkflow": 1,
    "workflowChains": [
      ["tool_001", "tool_002", "tool_003"],
      ["tool_003", "tool_004"],
      ["tool_003", "tool_005"],
      ["tool_003", "tool_006"],
      ["tool_003", "tool_007"],
      ["tool_003", "tool_008", "tool_009", "tool_010"],
      ["tool_008", "tool_009", "tool_010", "tool_012"],
      ["tool_011", "tool_012"]
    ],
    "sharedResources": [
      "access_token",
      "upload_token"
    ]
  },
  "securityAnalysis": {
    "hasDangerousTools": 0,
    "requiresPermissions": [
      "访问B站用户公开信息",
      "管理B站视频稿件",
      "获取用户数据（关注、粉丝、投稿）",
      "视频数据分析与管理"
    ],
    "riskFactors": [
      "涉及token和用户授权，需保护token存储安全性",
      "视频/图片本地读写，对文件路径有依赖且只要有操作系统读写权限即可（正常情况下风险极低）",
      "大量API写操作，如视频上传等，需关注接口权限授权范围"
    ],
    "recommendedRestrictions": [
      "不要将token暴露在不安全环境",
      "仅允许已授权用户使用所有API能力",
      "限制运行环境文件权限，严防意外覆盖、本地数据泄露",
      "建议定期清理本地token缓存",
      "确保操作系统和依赖库保持最新以规避已知安全漏洞"
    ]
  },
  "validationResults": {
    "dataConsistency": "所有步骤工具名称、描述、主字段完全一致",
    "toolCountMatch": "Step1、Step2、Step3工具数量均为12，总数一致",
    "idConsistency": "所有toolId均为tool_001-tool_012，格式规范且无缺失或重复",
    "completeness": "项目及工具全部字段齐全，支持全特性分析和多平台，数据完整"
  }
}


注释的内容：
